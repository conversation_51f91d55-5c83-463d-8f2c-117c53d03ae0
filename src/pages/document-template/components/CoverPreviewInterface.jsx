import React, { useState } from 'react';
import TextOverlayEditor from '../../../components/TextOverlayEditor/TextOverlayEditor';
import useTextOverlayEditor from '../../../hooks/useTextOverlayEditor';

/**
 * Cover Preview Interface
 * Shows cover-only preview for template design iteration
 * Replaces the full document preview during template selection phase
 */
const CoverPreviewInterface = ({
  selectedTemplate = null,
  documentData = {},
  coverPreviewData = null,
  loading = false,
  error = null,
  onBack = null,
  onProceedToExport = null,
  onPreviewUpdate = null,
  className = ''
}) => {
  const [isTextEditorVisible, setIsTextEditorVisible] = useState(false);

  // Initialize text overlay editor
  const {
    customizations,
    isPreviewUpdating,
    error: editorError,
    hasCustomizations,
    handleCustomizationChange,
    handleUndo,
    handleReset,
    canUndo
  } = useTextOverlayEditor(selectedTemplate, documentData, {
    onPreviewUpdate: onPreviewUpdate
  });

  // Apply loaded customizations to preview when template loads
  const [hasAppliedInitialCustomizations, setHasAppliedInitialCustomizations] = React.useState(false);

  React.useEffect(() => {
    if (selectedTemplate && hasCustomizations && onPreviewUpdate && !isPreviewUpdating && !hasAppliedInitialCustomizations) {
      console.log('🔄 Applying loaded customizations to preview', {
        templateId: selectedTemplate.id,
        customizationCount: Object.keys(customizations).length
      });

      // Apply the loaded customizations to update the preview
      onPreviewUpdate(selectedTemplate, documentData, customizations);
      setHasAppliedInitialCustomizations(true);
    }
  }, [selectedTemplate?.id, hasCustomizations, onPreviewUpdate, documentData, isPreviewUpdating, hasAppliedInitialCustomizations, customizations]);

  // Reset the applied flag when template changes
  React.useEffect(() => {
    setHasAppliedInitialCustomizations(false);
  }, [selectedTemplate?.id]);

  if (loading) {
    return (
      <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Generating cover preview...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Preview Error</h3>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!coverPreviewData) {
    return (
      <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <p className="text-gray-600">Please select a template to generate cover preview</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`cover-preview-interface flex flex-col h-full ${className}`}>
      {/* Preview Header */}
      <CoverPreviewHeader
        onBack={onBack}
        onProceedToExport={onProceedToExport}
        onToggleTextEditor={() => setIsTextEditorVisible(!isTextEditorVisible)}
        isTextEditorVisible={isTextEditorVisible}
        hasCustomizations={hasCustomizations}
        canUndo={canUndo}
        onUndo={handleUndo}
        onReset={handleReset}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Cover Preview Content */}
        <div className={`flex-1 bg-gray-100 overflow-auto transition-all duration-300 ${
          isTextEditorVisible ? 'lg:mr-80' : ''
        }`}>
          <div className="mt-4 mx-auto sm:mt-6 text-center max-w-2xl">
            <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">
              Cover Design Preview
              {isPreviewUpdating && (
                <span className="ml-2 inline-flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="ml-1 text-sm text-blue-600">Updating...</span>
                </span>
              )}
            </h2>
            {editorError && (
              <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
                {editorError}
              </div>
            )}
          </div>
          <CoverPreviewContainer
            coverPreviewData={coverPreviewData}
          />
        </div>

        {/* Text Overlay Editor */}
        <TextOverlayEditor
          template={selectedTemplate}
          customizations={customizations}
          onCustomizationChange={handleCustomizationChange}
          onReset={handleReset}
          isVisible={isTextEditorVisible}
          onToggleVisibility={setIsTextEditorVisible}
        />
      </div>
    </div>
  );
};

/**
 * Responsive Cover Preview Container
 * Handles mobile viewport constraints and proper scaling
 */
const CoverPreviewContainer = ({ coverPreviewData }) => {
  // Base dimensions for US Letter size (8.5" x 11")
  const baseWidth = 8.5; // inches
  const baseHeight = 11; // inches
  const baseWidthPx = baseWidth * 96; // 816px (96 DPI standard)
  const baseHeightPx = baseHeight * 96; // 1056px

  // Get viewport constraints with mobile-specific logic
  const getViewportConstraints = () => {
    if (typeof window === 'undefined') return { maxWidth: 1200, isMobile: false };

    const viewportWidth = document.documentElement.clientWidth || window.innerWidth;
    const viewportHeight = document.documentElement.clientHeight || window.innerHeight;

    let deviceType, margin;
    if (viewportWidth <= 480) {
      deviceType = 'small-mobile';
      margin = 16; // Small margin for small phones
    } else if (viewportWidth <= 640) {
      deviceType = 'large-mobile';
      margin = 24; // Medium margin for large phones
    } else if (viewportWidth <= 768) {
      deviceType = 'tablet-portrait';
      margin = 32; // Larger margin for tablets
    } else {
      deviceType = 'desktop';
      margin = 64; // Full margin for desktop
    }

    const isMobile = viewportWidth < 768;
    const maxWidth = viewportWidth - margin;
    const maxHeight = viewportHeight - 200; // Account for header and info sections

    return { maxWidth, maxHeight, isMobile, deviceType, viewportWidth, viewportHeight };
  };

  const { maxWidth, maxHeight, isMobile, deviceType } = getViewportConstraints();

  // Calculate responsive scaling
  let containerWidth, containerHeight, pageScale;

  if (isMobile) {
    // Mobile: Scale to fit viewport width with minimum readability scale
    const mobileMinScale = 0.4; // Minimum scale for mobile
    const widthScale = maxWidth / baseWidthPx;
    const heightScale = maxHeight / baseHeightPx;

    // Use the smaller scale to ensure it fits in both dimensions
    const calculatedScale = Math.min(widthScale, heightScale);
    pageScale = Math.max(mobileMinScale, calculatedScale);

    containerWidth = baseWidthPx * pageScale;
    containerHeight = baseHeightPx * pageScale;
  } else {
    // Desktop: Use a comfortable preview scale
    const desktopScale = 0.75;
    const widthScale = maxWidth / baseWidthPx;

    pageScale = Math.min(desktopScale, widthScale);
    containerWidth = baseWidthPx * pageScale;
    containerHeight = baseHeightPx * pageScale;
  }

  console.log('🔍 COVER PREVIEW DEBUG: Responsive scaling', {
    deviceType,
    viewport: { width: maxWidth, height: maxHeight },
    base: { width: baseWidthPx, height: baseHeightPx },
    calculated: { scale: pageScale, width: containerWidth, height: containerHeight },
    isMobile
  });

  return (
    <div className="cover-preview-container p-4 sm:p-6 md:p-8 flex items-center justify-center min-h-full">
      <div className="cover-preview-wrapper w-full flex flex-col items-center">
        {/* Responsive Cover Preview */}
        <div
          className="cover-preview-page shadow-lg overflow-hidden"
          style={{
            width: `${containerWidth}px`,
            height: `${containerHeight}px`,
            maxWidth: '100%',
            margin: '0 auto',
            position: 'relative'
          }}
        >
          <div
            className="cover-page-scaled"
            style={{
              width: `${baseWidthPx}px`,
              height: `${baseHeightPx}px`,
              transform: `scale(${pageScale})`,
              transformOrigin: 'center top',
              position: 'absolute',
              top: 0,
              left: '50%',
              marginLeft: `-${baseWidthPx / 2}px`
            }}
          >
            <div
              className="cover-content w-full h-full"
              dangerouslySetInnerHTML={{ __html: coverPreviewData.coverHTML }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Cover Preview Header Component
 */
const CoverPreviewHeader = ({
  onBack,
  onProceedToExport,
  onToggleTextEditor,
  isTextEditorVisible,
  hasCustomizations,
  canUndo,
  onUndo,
  onReset
}) => {
  return (
    <div className="cover-preview-header border-b border-gray-200 bg-white">
      {/* Mobile-First Responsive Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between px-4 py-2 gap-2 sm:gap-4">

        {/* Top Row on Mobile / Left Section on Desktop */}
        <div className="flex items-center justify-between sm:justify-start">
          {/* Back Button */}
          <button
            onClick={onBack}
            className="flex items-center space-x-1 sm:space-x-2 text-gray-600 hover:text-gray-800 transition-colors p-2 sm:p-0 -ml-2 sm:ml-0"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span className="text-sm sm:text-base hidden xs:inline sm:inline">Back</span>
            <span className="text-sm hidden sm:inline">to Templates</span>
          </button>

          {/* Export Button - Mobile Position */}
          <button
            onClick={onProceedToExport}
            className="sm:hidden bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>Export</span>
          </button>
        </div>

        {/* Desktop-Only Right Section */}
        <div className="hidden sm:flex items-center space-x-3">
          {/* Text Editor Toggle */}
          <button
            onClick={onToggleTextEditor}
            className={`px-3 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 ${
              isTextEditorVisible
                ? 'bg-blue-100 text-blue-700 border border-blue-300'
                : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
            }`}
            title="Edit text styling"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <span>Edit Text</span>
            {hasCustomizations && (
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
            )}
          </button>

          {/* Undo Button */}
          {canUndo && (
            <button
              onClick={onUndo}
              className="px-3 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              title="Undo last change"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
              </svg>
            </button>
          )}

          {/* Reset Button */}
          {hasCustomizations && (
            <button
              onClick={onReset}
              className="px-3 py-2 text-red-600 hover:text-red-800 transition-colors"
              title="Reset all changes"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          )}

          <button
            onClick={onProceedToExport}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 whitespace-nowrap"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
            <span>Proceed to Export</span>
          </button>
        </div>

        {/* Mobile Edit Button */}
        <div className="sm:hidden mt-2">
          <button
            onClick={onToggleTextEditor}
            className={`w-full px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2 ${
              isTextEditorVisible
                ? 'bg-blue-100 text-blue-700 border border-blue-300'
                : 'bg-gray-100 text-gray-700 border border-gray-300'
            }`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            <span>Edit Text Style</span>
            {hasCustomizations && (
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CoverPreviewInterface;
